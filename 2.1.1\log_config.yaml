# 日志配置文件
# 用于控制翻译软件的日志输出详细程度和格式

# 日志级别设置
log_level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# 日志显示设置
log_display:
  # 是否启用彩色输出
  enable_colors: true
  
  # 是否显示图标
  show_icons: true
  
  # 时间格式
  time_format: "%H:%M:%S"  # 简化时间格式，只显示时分秒
  
  # 消息最大长度（超过会被截断）
  max_message_length: 120
  
  # 是否显示模块名称
  show_module: false
  
  # 日志类别颜色配置
  level_colors:
    DEBUG: "\033[36m"     # 青色
    INFO: "\033[32m"      # 绿色
    WARNING: "\033[33m"   # 黄色
    ERROR: "\033[31m"     # 红色
    CRITICAL: "\033[35m"  # 紫色
  
  # 日志类别图标配置
  category_icons:
    翻译: "🔄"
    检测: "🔍"
    缓存: "💾"
    API: "🌐"
    配置: "⚙️"
    网络: "📡"
    错误: "❌"
    成功: "✅"
    警告: "⚠️"
  
  # 关键词高亮
  key_highlights:
    - "缓存"
    - "API"
    - "翻译"
    - "错误"
    - "检测"
    - "网络"
    - "配置"

# 日志过滤设置
log_filter:
  # 详细程度级别: minimal, normal, detailed, debug
  verbosity_level: normal
  
  # 在 minimal 级别下过滤的消息模式
  minimal_skip_patterns:
    - "【API响应原始文本】"
    - "【API响应JSON对象】"
    - "【构建提示词】"
    - "pycld2 检测结果"
    - "决策逻辑："
    - "基于特征补充候选"
    - "Token使用情况"
    - "当前模型温度:"
    - "进度指示器"
    - "替换操作过于频繁"
    - "记录上次翻译目标语言"
    - "隐藏GUI等待提示"
    - "【内存缓存更新】"
    - "【本地缓存更新】"
    - "已立即保存"
    - "输入框内容已替换为:"
    - "思考模式已禁用"
    - "明确禁用思考模式"
    - "发给大模型的完整提示词:"
    - "使用缓存的网络状态:"
    - "优先查询本地缓存"
    - "显示进度指示器"
    - "使用API密钥进行翻译请求:"
  
  # 在 normal 级别下额外过滤的消息模式
  normal_skip_patterns:
    - "API密钥解密成功"
    - "创建LRU缓存"
    - "缓存命中率"
  
  # 在 detailed 级别下额外过滤的消息模式
  detailed_skip_patterns: []

# 文件日志设置
file_logging:
  # 是否启用文件日志
  enabled: true
  
  # 日志文件路径
  log_file: "app.log"
  
  # 文件日志级别
  file_log_level: INFO
  
  # 文件大小限制（字节）
  max_bytes: 2097152  # 2MB
  
  # 备份文件数量
  backup_count: 3
  
  # 文件编码
  encoding: "utf-8"
  
  # 文件日志格式（不包含颜色和图标）
  file_format: "%(asctime)s - %(levelname)s - %(message)s"

# 特殊日志处理
special_handling:
  # 翻译结果是否单独显示
  highlight_translation_result: true
  
  # 错误消息是否加粗显示
  bold_errors: true
  
  # 是否在翻译开始和结束时显示分隔线
  show_translation_separators: false
  
  # 是否显示翻译统计信息
  show_translation_stats: false
