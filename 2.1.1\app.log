2025-05-30 21:30:24,433 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 21:30:24,648 - INFO - 语言模式配置已加载。
2025-05-30 21:30:24,648 - INFO - 语言模式配置已加载。
2025-05-30 21:30:24,649 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 21:30:24,649 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 21:30:24,654 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.0\translation_cache.db
2025-05-30 21:30:24,666 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 21:30:24,666 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 21:30:24,668 - INFO - 菜单已显示，等待用户输入。
2025-05-30 21:30:24,745 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 21:30:25,467 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 21:30:26,611 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
